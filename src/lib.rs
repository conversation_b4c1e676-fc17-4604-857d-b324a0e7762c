use ring::{error, signature};
use std::ffi::c_char;
use std::slice;

/// ECDSA verification result codes
#[repr(C)]
#[derive(Debug)]
pub enum VerifyResult {
    Success = 0,
    InvalidSignature = 1,
    InvalidPublicKey = 2,
    InvalidInput = 3,
    InternalError = 4,
}

/// Verify ECDSA P-256 signature
///
/// # Parameters
/// - `public_key_ptr`: Pointer to the public key bytes (64 bytes, uncompressed format without 0x04 prefix)
/// - `public_key_len`: Length of public key (should be 64)
/// - `message_ptr`: Pointer to the message bytes
/// - `message_len`: Length of the message
/// - `signature_ptr`: Pointer to the signature bytes (64 bytes, r||s format)
/// - `signature_len`: Length of signature (should be 64)
///
/// # Returns
/// VerifyResult enum value
#[no_mangle]
pub extern "C" fn ecdsa_p256_verify(
    public_key_ptr: *const u8,
    public_key_len: usize,
    message_ptr: *const u8,
    message_len: usize,
    signature_ptr: *const u8,
    signature_len: usize,
) -> VerifyResult {
    // Input validation
    if public_key_ptr.is_null() || message_ptr.is_null() || signature_ptr.is_null() {
        return VerifyResult::InvalidInput;
    }

    if public_key_len != 64 || signature_len != 64 {
        return VerifyResult::InvalidInput;
    }

    if message_len == 0 {
        return VerifyResult::InvalidInput;
    }

    // Convert raw pointers to slices
    let public_key_bytes = unsafe { slice::from_raw_parts(public_key_ptr, public_key_len) };
    let message_bytes = unsafe { slice::from_raw_parts(message_ptr, message_len) };
    let signature_bytes = unsafe { slice::from_raw_parts(signature_ptr, signature_len) };

    // Perform ECDSA verification
    match verify_ecdsa_p256_internal(public_key_bytes, message_bytes, signature_bytes) {
        Ok(()) => VerifyResult::Success,
        Err(VerifyError::InvalidSignature) => VerifyResult::InvalidSignature,
        Err(VerifyError::InvalidPublicKey) => VerifyResult::InvalidPublicKey,
        Err(VerifyError::InternalError) => VerifyResult::InternalError,
    }
}

/// Internal error types
#[derive(Debug)]
enum VerifyError {
    InvalidSignature,
    InvalidPublicKey,
    InternalError,
}

/// Internal ECDSA verification function
fn verify_ecdsa_p256_internal(
    public_key_bytes: &[u8],
    message: &[u8],
    signature_bytes: &[u8],
) -> Result<(), VerifyError> {
    // Construct the uncompressed public key with 0x04 prefix
    let mut uncompressed_key = Vec::with_capacity(65);
    uncompressed_key.push(0x04); // Uncompressed point indicator
    uncompressed_key.extend_from_slice(public_key_bytes);

    // Create public key from uncompressed bytes
    let public_key =
        signature::UnparsedPublicKey::new(&signature::ECDSA_P256_SHA256_FIXED, &uncompressed_key);

    // Verify the signature
    match public_key.verify(message, signature_bytes) {
        Ok(()) => Ok(()),
        Err(error::Unspecified) => Err(VerifyError::InvalidSignature),
    }
}

/// Get version string
#[no_mangle]
pub extern "C" fn get_version() -> *const c_char {
    "cert-verify-rs 0.1.0\0".as_ptr() as *const c_char
}

/// Initialize the library (placeholder for future use)
#[no_mangle]
pub extern "C" fn init_library() -> i32 {
    0 // Success
}

/// Cleanup the library (placeholder for future use)
#[no_mangle]
pub extern "C" fn cleanup_library() {
    // Nothing to cleanup currently
}

#[cfg(test)]
mod tests {
    use super::*;

    // Test vectors from NIST or other reliable sources
    const TEST_PUBLIC_KEY: [u8; 64] = [
        // X coordinate (32 bytes)
        0x60, 0xfe, 0xd4, 0xba, 0x25, 0x5a, 0x9d, 0x31, 0xc9, 0x61, 0xeb, 0x74, 0xc6, 0x35, 0x6d,
        0x68, 0xc0, 0x49, 0xb8, 0x92, 0x3b, 0x61, 0xfa, 0x6c, 0xe6, 0x69, 0x62, 0x2e, 0x60, 0xf2,
        0x9f, 0xb6, // Y coordinate (32 bytes)
        0x79, 0x03, 0xfe, 0x10, 0x08, 0xb8, 0xbc, 0x99, 0xa4, 0x1a, 0xe9, 0xe9, 0x56, 0x28, 0xbc,
        0x64, 0xf2, 0xf1, 0xb2, 0x0c, 0x2d, 0x7e, 0x9f, 0x51, 0x77, 0xa3, 0xc2, 0x94, 0xd4, 0x46,
        0x22, 0x99,
    ];

    const TEST_MESSAGE: &[u8] = b"sample";

    const TEST_SIGNATURE: [u8; 64] = [
        // R component (32 bytes)
        0xef, 0xd4, 0x8b, 0x2a, 0xac, 0xb6, 0xa8, 0xfd, 0x11, 0x40, 0xdd, 0x9c, 0xd4, 0x5e, 0x81,
        0xd6, 0x9d, 0x2c, 0x87, 0x7b, 0x56, 0xaa, 0xf9, 0x91, 0xc3, 0x4d, 0x0e, 0xa8, 0x4e, 0xaf,
        0x37, 0x16, // S component (32 bytes)
        0xf7, 0xcb, 0x1c, 0x94, 0x2d, 0x65, 0x7c, 0x41, 0xd4, 0x36, 0xc7, 0xa1, 0xb6, 0xe2, 0x9f,
        0x65, 0xf3, 0xe9, 0x00, 0xdb, 0xb9, 0xaf, 0xf4, 0x06, 0x4d, 0xc4, 0xab, 0x2f, 0x84, 0x3a,
        0xcd, 0xa8,
    ];

    #[test]
    fn test_ecdsa_verify_valid_signature() {
        let result = ecdsa_p256_verify(
            TEST_PUBLIC_KEY.as_ptr(),
            TEST_PUBLIC_KEY.len(),
            TEST_MESSAGE.as_ptr(),
            TEST_MESSAGE.len(),
            TEST_SIGNATURE.as_ptr(),
            TEST_SIGNATURE.len(),
        );

        // Note: This test may fail with the current test vectors as they are placeholders
        // In a real implementation, you would use actual valid test vectors
        match result {
            VerifyResult::Success => println!("Signature verification succeeded"),
            VerifyResult::InvalidSignature => {
                println!("Invalid signature (expected with placeholder test vectors)")
            }
            other => panic!("Unexpected result: {:?}", other),
        }
    }

    #[test]
    fn test_ecdsa_verify_invalid_input() {
        // Test null pointer
        let result = ecdsa_p256_verify(
            std::ptr::null(),
            64,
            TEST_MESSAGE.as_ptr(),
            TEST_MESSAGE.len(),
            TEST_SIGNATURE.as_ptr(),
            TEST_SIGNATURE.len(),
        );
        assert!(matches!(result, VerifyResult::InvalidInput));

        // Test wrong key length
        let result = ecdsa_p256_verify(
            TEST_PUBLIC_KEY.as_ptr(),
            32, // Wrong length
            TEST_MESSAGE.as_ptr(),
            TEST_MESSAGE.len(),
            TEST_SIGNATURE.as_ptr(),
            TEST_SIGNATURE.len(),
        );
        assert!(matches!(result, VerifyResult::InvalidInput));

        // Test wrong signature length
        let result = ecdsa_p256_verify(
            TEST_PUBLIC_KEY.as_ptr(),
            TEST_PUBLIC_KEY.len(),
            TEST_MESSAGE.as_ptr(),
            TEST_MESSAGE.len(),
            TEST_SIGNATURE.as_ptr(),
            32, // Wrong length
        );
        assert!(matches!(result, VerifyResult::InvalidInput));

        // Test empty message
        let result = ecdsa_p256_verify(
            TEST_PUBLIC_KEY.as_ptr(),
            TEST_PUBLIC_KEY.len(),
            TEST_MESSAGE.as_ptr(),
            0, // Empty message
            TEST_SIGNATURE.as_ptr(),
            TEST_SIGNATURE.len(),
        );
        assert!(matches!(result, VerifyResult::InvalidInput));
    }

    #[test]
    fn test_version_function() {
        let version_ptr = get_version();
        assert!(!version_ptr.is_null());

        let version_str = unsafe {
            std::ffi::CStr::from_ptr(version_ptr)
                .to_str()
                .expect("Version string should be valid UTF-8")
        };

        assert!(version_str.contains("cert-verify-rs"));
        assert!(version_str.contains("0.1.0"));
    }

    #[test]
    fn test_library_init_cleanup() {
        let init_result = init_library();
        assert_eq!(init_result, 0);

        cleanup_library(); // Should not panic
    }
}
