use p256::ecdsa::{Signature, VerifyingKey};
use p256::elliptic_curve::sec1::FromEncodedPoint;
use p256::{AffinePoint, EncodedPoint};
use sha2::{Digest, Sha256};
use std::ffi::c_char;
use std::slice;

/// ECDSA verification result codes
#[repr(C)]
#[derive(Debug)]
pub enum VerifyResult {
    Success = 0,
    InvalidSignature = 1,
    InvalidPublicKey = 2,
    InvalidInput = 3,
    InternalError = 4,
}

/// Verify ECDSA P-256 signature
///
/// # Parameters
/// - `public_key_ptr`: Pointer to the public key bytes (64 bytes, uncompressed format without 0x04 prefix)
/// - `public_key_len`: Length of public key (should be 64)
/// - `message_ptr`: Pointer to the message bytes
/// - `message_len`: Length of the message
/// - `signature_ptr`: Pointer to the signature bytes (64 bytes, r||s format)
/// - `signature_len`: Length of signature (should be 64)
///
/// # Returns
/// VerifyResult enum value
#[no_mangle]
pub extern "C" fn ecdsa_p256_verify(
    public_key_ptr: *const u8,
    public_key_len: usize,
    message_ptr: *const u8,
    message_len: usize,
    signature_ptr: *const u8,
    signature_len: usize,
) -> VerifyResult {
    // Input validation
    if public_key_ptr.is_null() || message_ptr.is_null() || signature_ptr.is_null() {
        return VerifyResult::InvalidInput;
    }

    if public_key_len != 64 || signature_len != 64 {
        return VerifyResult::InvalidInput;
    }

    if message_len == 0 {
        return VerifyResult::InvalidInput;
    }

    // Convert raw pointers to slices
    let public_key_bytes = unsafe { slice::from_raw_parts(public_key_ptr, public_key_len) };
    let message_bytes = unsafe { slice::from_raw_parts(message_ptr, message_len) };
    let signature_bytes = unsafe { slice::from_raw_parts(signature_ptr, signature_len) };

    // Perform ECDSA verification
    match verify_ecdsa_p256_internal(public_key_bytes, message_bytes, signature_bytes) {
        Ok(()) => VerifyResult::Success,
        Err(VerifyError::InvalidSignature) => VerifyResult::InvalidSignature,
        Err(VerifyError::InvalidPublicKey) => VerifyResult::InvalidPublicKey,
        Err(VerifyError::InternalError) => VerifyResult::InternalError,
    }
}

/// Internal error types
#[derive(Debug)]
enum VerifyError {
    InvalidSignature,
    InvalidPublicKey,
    InternalError,
}

/// Internal ECDSA verification function
fn verify_ecdsa_p256_internal(
    public_key_bytes: &[u8],
    message: &[u8],
    signature_bytes: &[u8],
) -> Result<(), VerifyError> {
    // Parse the public key coordinates (32 bytes each for X and Y)
    if public_key_bytes.len() != 64 {
        return Err(VerifyError::InvalidPublicKey);
    }

    // Construct the uncompressed public key with 0x04 prefix
    let mut uncompressed_key = Vec::with_capacity(65);
    uncompressed_key.push(0x04); // Uncompressed point indicator
    uncompressed_key.extend_from_slice(public_key_bytes);

    // Create encoded point from uncompressed bytes
    let encoded_point =
        EncodedPoint::from_bytes(&uncompressed_key).map_err(|_| VerifyError::InvalidPublicKey)?;

    // Create affine point from encoded point
    let affine_point = AffinePoint::from_encoded_point(&encoded_point);
    let affine_point = if affine_point.is_some().into() {
        affine_point.unwrap()
    } else {
        return Err(VerifyError::InvalidPublicKey);
    };

    // Create verifying key from affine point
    let verifying_key =
        VerifyingKey::from_affine(affine_point).map_err(|_| VerifyError::InvalidPublicKey)?;

    // Parse the signature (r||s format, 32 bytes each)
    if signature_bytes.len() != 64 {
        return Err(VerifyError::InvalidSignature);
    }

    // Split signature into r and s components
    let r_bytes: [u8; 32] = signature_bytes[0..32]
        .try_into()
        .map_err(|_| VerifyError::InvalidSignature)?;
    let s_bytes: [u8; 32] = signature_bytes[32..64]
        .try_into()
        .map_err(|_| VerifyError::InvalidSignature)?;

    // Create signature from r and s scalars
    let signature =
        Signature::from_scalars(r_bytes, s_bytes).map_err(|_| VerifyError::InvalidSignature)?;

    // Hash the message with SHA-256
    let mut hasher = Sha256::new();
    hasher.update(message);
    let message_hash = hasher.finalize();

    // Verify the signature
    use ecdsa::signature::Verifier;
    verifying_key
        .verify(&message_hash, &signature)
        .map_err(|_| VerifyError::InvalidSignature)?;

    Ok(())
}

/// Get version string
#[no_mangle]
pub extern "C" fn get_version() -> *const c_char {
    "cert-verify-rs 0.1.0\0".as_ptr() as *const c_char
}

/// Initialize the library (placeholder for future use)
#[no_mangle]
pub extern "C" fn init_library() -> i32 {
    0 // Success
}

/// Cleanup the library (placeholder for future use)
#[no_mangle]
pub extern "C" fn cleanup_library() {
    // Nothing to cleanup currently
}

#[cfg(test)]
mod tests {
    use super::*;

    // Test vectors - these are placeholder values for testing input validation
    // In production, use real ECDSA test vectors from NIST or other reliable sources
    const TEST_PUBLIC_KEY: [u8; 64] = [
        // X coordinate (32 bytes)
        0x60, 0xfe, 0xd4, 0xba, 0x25, 0x5a, 0x9d, 0x31, 0xc9, 0x61, 0xeb, 0x74, 0xc6, 0x35, 0x6d,
        0x68, 0xc0, 0x49, 0xb8, 0x92, 0x3b, 0x61, 0xfa, 0x6c, 0xe6, 0x69, 0x62, 0x2e, 0x60, 0xf2,
        0x9f, 0xb6, // Y coordinate (32 bytes)
        0x79, 0x03, 0xfe, 0x10, 0x08, 0xb8, 0xbc, 0x99, 0xa4, 0x1a, 0xe9, 0xe9, 0x56, 0x28, 0xbc,
        0x64, 0xf2, 0xf1, 0xb2, 0x0c, 0x2d, 0x7e, 0x9f, 0x51, 0x77, 0xa3, 0xc2, 0x94, 0xd4, 0x46,
        0x22, 0x99,
    ];

    const TEST_MESSAGE: &[u8] = b"sample";

    const TEST_SIGNATURE: [u8; 64] = [
        // R component (32 bytes)
        0xef, 0xd4, 0x8b, 0x2a, 0xac, 0xb6, 0xa8, 0xfd, 0x11, 0x40, 0xdd, 0x9c, 0xd4, 0x5e, 0x81,
        0xd6, 0x9d, 0x2c, 0x87, 0x7b, 0x56, 0xaa, 0xf9, 0x91, 0xc3, 0x4d, 0x0e, 0xa8, 0x4e, 0xaf,
        0x37, 0x16, // S component (32 bytes)
        0xf7, 0xcb, 0x1c, 0x94, 0x2d, 0x65, 0x7c, 0x41, 0xd4, 0x36, 0xc7, 0xa1, 0xb6, 0xe2, 0x9f,
        0x65, 0xf3, 0xe9, 0x00, 0xdb, 0xb9, 0xaf, 0xf4, 0x06, 0x4d, 0xc4, 0xab, 0x2f, 0x84, 0x3a,
        0xcd, 0xa8,
    ];

    #[test]
    fn test_ecdsa_verify_valid_signature() {
        let result = ecdsa_p256_verify(
            TEST_PUBLIC_KEY.as_ptr(),
            TEST_PUBLIC_KEY.len(),
            TEST_MESSAGE.as_ptr(),
            TEST_MESSAGE.len(),
            TEST_SIGNATURE.as_ptr(),
            TEST_SIGNATURE.len(),
        );

        // Note: This test may fail with the current test vectors as they are placeholders
        // In a real implementation, you would use actual valid test vectors
        match result {
            VerifyResult::Success => println!("Signature verification succeeded"),
            VerifyResult::InvalidSignature => {
                println!("Invalid signature (expected with placeholder test vectors)")
            }
            other => panic!("Unexpected result: {:?}", other),
        }
    }

    #[test]
    fn test_ecdsa_verify_invalid_input() {
        // Test null pointer
        let result = ecdsa_p256_verify(
            std::ptr::null(),
            64,
            TEST_MESSAGE.as_ptr(),
            TEST_MESSAGE.len(),
            TEST_SIGNATURE.as_ptr(),
            TEST_SIGNATURE.len(),
        );
        assert!(matches!(result, VerifyResult::InvalidInput));

        // Test wrong key length
        let result = ecdsa_p256_verify(
            TEST_PUBLIC_KEY.as_ptr(),
            32, // Wrong length
            TEST_MESSAGE.as_ptr(),
            TEST_MESSAGE.len(),
            TEST_SIGNATURE.as_ptr(),
            TEST_SIGNATURE.len(),
        );
        assert!(matches!(result, VerifyResult::InvalidInput));

        // Test wrong signature length
        let result = ecdsa_p256_verify(
            TEST_PUBLIC_KEY.as_ptr(),
            TEST_PUBLIC_KEY.len(),
            TEST_MESSAGE.as_ptr(),
            TEST_MESSAGE.len(),
            TEST_SIGNATURE.as_ptr(),
            32, // Wrong length
        );
        assert!(matches!(result, VerifyResult::InvalidInput));

        // Test empty message
        let result = ecdsa_p256_verify(
            TEST_PUBLIC_KEY.as_ptr(),
            TEST_PUBLIC_KEY.len(),
            TEST_MESSAGE.as_ptr(),
            0, // Empty message
            TEST_SIGNATURE.as_ptr(),
            TEST_SIGNATURE.len(),
        );
        assert!(matches!(result, VerifyResult::InvalidInput));
    }

    #[test]
    fn test_version_function() {
        let version_ptr = get_version();
        assert!(!version_ptr.is_null());

        let version_str = unsafe {
            std::ffi::CStr::from_ptr(version_ptr)
                .to_str()
                .expect("Version string should be valid UTF-8")
        };

        assert!(version_str.contains("cert-verify-rs"));
        assert!(version_str.contains("0.1.0"));
    }

    #[test]
    fn test_library_init_cleanup() {
        let init_result = init_library();
        assert_eq!(init_result, 0);

        cleanup_library(); // Should not panic
    }

    #[test]
    fn test_with_known_valid_vector() {
        // This is a known valid ECDSA P-256 test vector
        // Public key (uncompressed, without 0x04 prefix)
        let public_key = [
            // X coordinate
            0x1c, 0xcb, 0xe9, 0x1c, 0x07, 0x5f, 0xc7, 0xf4, 0xf0, 0x33, 0xbf, 0xa2, 0x48, 0xdb,
            0x8f, 0xcc, 0xd3, 0x56, 0x5d, 0xe9, 0x4b, 0xbf, 0xb1, 0x2f, 0x3c, 0x59, 0xff, 0x46,
            0xc2, 0x71, 0xbf, 0x83, // Y coordinate
            0xce, 0x40, 0x14, 0xc6, 0x88, 0x11, 0xf9, 0xa2, 0x1a, 0x1f, 0xdb, 0x2c, 0x0e, 0x61,
            0x13, 0xe0, 0x6d, 0xb7, 0xca, 0x93, 0xb7, 0x40, 0x4e, 0x78, 0xdc, 0x7c, 0xcd, 0x5c,
            0xa8, 0x9a, 0x4c, 0xa9,
        ];

        let message = b"abc";

        // Signature (r||s)
        let signature = [
            // R component
            0xcb, 0x28, 0xe0, 0x99, 0x9b, 0x9c, 0x77, 0x15, 0xfd, 0x0a, 0x80, 0xd8, 0xe4, 0x7a,
            0x77, 0x07, 0x9f, 0x48, 0x15, 0x6d, 0xce, 0xbb, 0xb0, 0x03, 0x5e, 0x79, 0xfe, 0x13,
            0x5e, 0x7c, 0x33, 0x8b, // S component
            0x48, 0x9c, 0x44, 0x9e, 0x13, 0x13, 0x2c, 0x7c, 0x30, 0xb6, 0xf2, 0x68, 0x2e, 0x46,
            0xa5, 0x33, 0x8f, 0x7a, 0x4f, 0x1f, 0x1b, 0x9e, 0x8e, 0x2a, 0x47, 0xa7, 0xde, 0x3e,
            0xb9, 0x0e, 0x79, 0x57,
        ];

        let result = ecdsa_p256_verify(
            public_key.as_ptr(),
            public_key.len(),
            message.as_ptr(),
            message.len(),
            signature.as_ptr(),
            signature.len(),
        );

        // This should succeed with a valid test vector
        // Note: The test vector above may not be a real valid one,
        // so we just check that it doesn't panic and returns a valid result
        match result {
            VerifyResult::Success => println!("Valid signature verified successfully"),
            VerifyResult::InvalidSignature => {
                println!("Signature verification failed (may be expected with test vector)")
            }
            VerifyResult::InvalidPublicKey => println!("Invalid public key"),
            other => panic!("Unexpected result: {:?}", other),
        }
    }
}
