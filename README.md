# cert-verify-rs

A Rust library for ECDSA P-256 signature verification, designed to be compatible with Android 4.4 and Rust 1.67. The library can be built as a static library for use in C programs.

## Features

- ECDSA P-256 (secp256r1) signature verification
- C FFI interface for easy integration with C/C++ programs
- Static library output for easy linking
- Compatible with Android 4.4+ and Rust 1.67+
- Cross-platform support (Linux, macOS, Windows, Android)

## Requirements

- Rust 1.67 or later
- For Android builds: Android NDK

## Building

### Host Platform

```bash
# Build the static library
cargo build --release

# Or use the Makefile
make build
```

The static library will be available at `target/release/libcert_verify_rs.a`.

### Android

First, install the Android targets:

```bash
make install-android-targets
```

Then build for Android:

```bash
# Build for all Android architectures
make android-build

# Or build for specific targets
cargo build --release --target aarch64-linux-android
cargo build --release --target armv7-linux-androideabi
cargo build --release --target x86_64-linux-android
cargo build --release --target i686-linux-android
```

## Usage

### C Interface

Include the header file and link against the static library:

```c
#include "cert_verify_rs.h"

// Example usage
uint8_t public_key[64] = { /* 64-byte public key without 0x04 prefix */ };
uint8_t message[] = "Hello, World!";
uint8_t signature[64] = { /* 64-byte signature in r||s format */ };

VerifyResult result = ecdsa_p256_verify(
    public_key, sizeof(public_key),
    message, sizeof(message) - 1,  // Exclude null terminator
    signature, sizeof(signature)
);

if (result == VERIFY_SUCCESS) {
    printf("Signature is valid!\n");
} else {
    printf("Signature verification failed: %d\n", result);
}
```

### Compilation

```bash
# Compile your C program
gcc -o example examples/c_example.c -L./target/release -lcert_verify_rs -lpthread -ldl -lm
```

For Android, use the appropriate NDK toolchain.
