# cert-verify-rs

A Rust library for ECDSA P-256 signature verification using the `p256` crate, designed to be compatible with Android 4.4 and Rust 1.67. The library can be built as a static library for use in C programs.

## Features

- ECDSA P-256 (secp256r1) signature verification
- C FFI interface for easy integration with C/C++ programs
- Static library output for easy linking
- Compatible with Android 4.4+ and Rust 1.67+
- Cross-platform support (Linux, macOS, Windows, Android)

## Requirements

- Rust 1.67 or later
- For Android builds: Android NDK

## Building

### Host Platform

```bash
# Build the static library
cargo build --release

# Or use the Makefile
make build
```

The static library will be available at `target/release/libcert_verify_rs.a`.

### Android

First, install the Android targets:

```bash
make install-android-targets
```

Then build for Android:

```bash
# Build for all Android architectures
make android-build

# Or build for specific targets
cargo build --release --target aarch64-linux-android
cargo build --release --target armv7-linux-androideabi
cargo build --release --target x86_64-linux-android
cargo build --release --target i686-linux-android
```

## Usage

### C Interface

Include the header file and link against the static library:

```c
#include "cert_verify_rs.h"

// Example usage
uint8_t public_key[64] = { /* 64-byte public key without 0x04 prefix */ };
uint8_t message[] = "Hello, World!";
uint8_t signature[64] = { /* 64-byte signature in r||s format */ };

VerifyResult result = ecdsa_p256_verify(
    public_key, sizeof(public_key),
    message, sizeof(message) - 1,  // Exclude null terminator
    signature, sizeof(signature)
);

if (result == VERIFY_SUCCESS) {
    printf("Signature is valid!\n");
} else {
    printf("Signature verification failed: %d\n", result);
}
```

### Compilation

```bash
# Compile your C program
gcc -o example examples/c_example.c -L./target/release -lcert_verify_rs -lpthread -ldl -lm
```

For Android, use the appropriate NDK toolchain.

## API Reference

### Functions

#### `ecdsa_p256_verify`

Verifies an ECDSA P-256 signature.

**Parameters:**
- `public_key_ptr`: Pointer to 64-byte public key (X||Y coordinates, no 0x04 prefix)
- `public_key_len`: Length of public key (must be 64)
- `message_ptr`: Pointer to message bytes
- `message_len`: Length of message
- `signature_ptr`: Pointer to 64-byte signature (R||S format)
- `signature_len`: Length of signature (must be 64)

**Returns:** `VerifyResult` enum value

#### `get_version`

Returns the library version string.

#### `init_library`

Initializes the library (currently a no-op, reserved for future use).

#### `cleanup_library`

Cleans up library resources (currently a no-op, reserved for future use).

### Result Codes

```c
typedef enum {
    VERIFY_SUCCESS = 0,
    VERIFY_INVALID_SIGNATURE = 1,
    VERIFY_INVALID_PUBLIC_KEY = 2,
    VERIFY_INVALID_INPUT = 3,
    VERIFY_INTERNAL_ERROR = 4
} VerifyResult;
```

## Data Formats

### Public Key Format

- 64 bytes total
- First 32 bytes: X coordinate of the public key point
- Last 32 bytes: Y coordinate of the public key point
- No 0x04 uncompressed point prefix

### Signature Format

- 64 bytes total
- First 32 bytes: R component of the signature
- Last 32 bytes: S component of the signature
- Big-endian byte order

### Message

- Raw bytes of the message to be verified
- The library uses SHA-256 internally for hashing

## Testing

Run the Rust tests:

```bash
cargo test
```

Build and run the C example:

```bash
make build
gcc -o c_example examples/c_example.c -L./target/release -lcert_verify_rs -lpthread -ldl -lm
./c_example
```

## Implementation Details

This library uses the following Rust crates (compatible with Rust 1.67):

- **`p256 v0.9`**: Pure Rust implementation of the NIST P-256 elliptic curve
- **`ecdsa v0.13`**: Generic ECDSA signature verification
- **`sha2 v0.9`**: SHA-256 hashing implementation

The implementation provides:
- Pure Rust cryptographic operations (no C dependencies)
- Constant-time operations to prevent timing attacks
- Memory-safe implementation
- Cross-platform compatibility

## Security Notes

- This library is designed for signature verification only
- Always validate input parameters before calling the verification function
- Use proper test vectors for validation in production environments
- The library uses pure Rust cryptographic implementations
- All operations are designed to be constant-time to prevent side-channel attacks

## License

This project is licensed under the MIT License - see the LICENSE file for details.
