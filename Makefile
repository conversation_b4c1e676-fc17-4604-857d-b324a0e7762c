# Makefile for cert-verify-rs

# Default target
.PHONY: all clean build test android-build

# Build for host platform
all: build

# Build the static library
build:
	cargo build --release

# Build for Android (requires Android NDK)
android-build:
	# For Android ARM64
	cargo build --release --target aarch64-linux-android
	# For Android ARM
	cargo build --release --target armv7-linux-androideabi
	# For Android x86_64
	cargo build --release --target x86_64-linux-android
	# For Android x86
	cargo build --release --target i686-linux-android

# Run tests
test:
	cargo test

# Clean build artifacts
clean:
	cargo clean

# Install Android targets (run once)
install-android-targets:
	rustup target add aarch64-linux-android
	rustup target add armv7-linux-androideabi
	rustup target add x86_64-linux-android
	rustup target add i686-linux-android

# Show library locations
show-libs:
	@echo "Host static library:"
	@find target/release -name "*.a" -type f 2>/dev/null || echo "Not built yet"
	@echo ""
	@echo "Android libraries:"
	@find target -name "*.a" -path "*/android*" -type f 2>/dev/null || echo "Not built yet"

# Create distribution package
dist: build
	mkdir -p dist/lib dist/include
	cp target/release/libcert_verify_rs.a dist/lib/
	cp include/cert_verify_rs.h dist/include/
	@echo "Distribution package created in dist/"
