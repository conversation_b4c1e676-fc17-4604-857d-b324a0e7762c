#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "../include/cert_verify_rs.h"

// Example test vectors (these are placeholder values)
static const uint8_t test_public_key[64] = {
    // X coordinate (32 bytes)
    0x60, 0xfe, 0xd4, 0xba, 0x25, 0x5a, 0x9d, 0x31,
    0xc9, 0x61, 0xeb, 0x74, 0xc6, 0x35, 0x6d, 0x68,
    0xc0, 0x49, 0xb8, 0x92, 0x3b, 0x61, 0xfa, 0x6c,
    0xe6, 0x69, 0x62, 0x2e, 0x60, 0xf2, 0x9f, 0xb6,
    // Y coordinate (32 bytes)
    0x79, 0x03, 0xfe, 0x10, 0x08, 0xb8, 0xbc, 0x99,
    0xa4, 0x1a, 0xe9, 0xe9, 0x56, 0x28, 0xbc, 0x64,
    0xf2, 0xf1, 0xb2, 0x0c, 0x2d, 0x7e, 0x9f, 0x51,
    0x77, 0xa3, 0xc2, 0x94, 0xd4, 0x46, 0x22, 0x99,
};

static const uint8_t test_message[] = "sample";
static const size_t test_message_len = sizeof(test_message) - 1; // Exclude null terminator

static const uint8_t test_signature[64] = {
    // R component (32 bytes)
    0xef, 0xd4, 0x8b, 0x2a, 0xac, 0xb6, 0xa8, 0xfd,
    0x11, 0x40, 0xdd, 0x9c, 0xd4, 0x5e, 0x81, 0xd6,
    0x9d, 0x2c, 0x87, 0x7b, 0x56, 0xaa, 0xf9, 0x91,
    0xc3, 0x4d, 0x0e, 0xa8, 0x4e, 0xaf, 0x37, 0x16,
    // S component (32 bytes)
    0xf7, 0xcb, 0x1c, 0x94, 0x2d, 0x65, 0x7c, 0x41,
    0xd4, 0x36, 0xc7, 0xa1, 0xb6, 0xe2, 0x9f, 0x65,
    0xf3, 0xe9, 0x00, 0xdb, 0xb9, 0xaf, 0xf4, 0x06,
    0x4d, 0xc4, 0xab, 0x2f, 0x84, 0x3a, 0xcd, 0xa8,
};

const char* verify_result_to_string(VerifyResult result) {
    switch (result) {
        case VERIFY_SUCCESS:
            return "SUCCESS";
        case VERIFY_INVALID_SIGNATURE:
            return "INVALID_SIGNATURE";
        case VERIFY_INVALID_PUBLIC_KEY:
            return "INVALID_PUBLIC_KEY";
        case VERIFY_INVALID_INPUT:
            return "INVALID_INPUT";
        case VERIFY_INTERNAL_ERROR:
            return "INTERNAL_ERROR";
        default:
            return "UNKNOWN";
    }
}

void print_hex(const uint8_t* data, size_t len) {
    for (size_t i = 0; i < len; i++) {
        printf("%02x", data[i]);
        if (i % 16 == 15) printf("\n");
        else if (i % 8 == 7) printf("  ");
        else printf(" ");
    }
    if (len % 16 != 0) printf("\n");
}

int main() {
    printf("ECDSA P-256 Signature Verification Example\n");
    printf("==========================================\n\n");

    // Initialize library
    int init_result = init_library();
    if (init_result != 0) {
        printf("Failed to initialize library: %d\n", init_result);
        return 1;
    }

    // Get version
    const char* version = get_version();
    printf("Library version: %s\n\n", version);

    // Print test data
    printf("Public Key (64 bytes):\n");
    print_hex(test_public_key, sizeof(test_public_key));
    printf("\n");

    printf("Message (%zu bytes): \"%.*s\"\n", test_message_len, (int)test_message_len, test_message);
    printf("Message hex:\n");
    print_hex(test_message, test_message_len);
    printf("\n");

    printf("Signature (64 bytes):\n");
    print_hex(test_signature, sizeof(test_signature));
    printf("\n");

    // Perform verification
    printf("Performing ECDSA P-256 verification...\n");
    VerifyResult result = ecdsa_p256_verify(
        test_public_key, sizeof(test_public_key),
        test_message, test_message_len,
        test_signature, sizeof(test_signature)
    );

    printf("Verification result: %s\n", verify_result_to_string(result));

    if (result == VERIFY_SUCCESS) {
        printf("✓ Signature is valid!\n");
    } else {
        printf("✗ Signature verification failed.\n");
        printf("Note: This is expected with placeholder test vectors.\n");
        printf("Use real ECDSA test vectors for actual validation.\n");
    }

    // Test error cases
    printf("\nTesting error cases:\n");
    
    // Test with null pointer
    result = ecdsa_p256_verify(
        NULL, sizeof(test_public_key),
        test_message, test_message_len,
        test_signature, sizeof(test_signature)
    );
    printf("Null public key: %s\n", verify_result_to_string(result));

    // Test with wrong key length
    result = ecdsa_p256_verify(
        test_public_key, 32, // Wrong length
        test_message, test_message_len,
        test_signature, sizeof(test_signature)
    );
    printf("Wrong key length: %s\n", verify_result_to_string(result));

    // Cleanup
    cleanup_library();
    printf("\nLibrary cleanup completed.\n");

    return 0;
}
