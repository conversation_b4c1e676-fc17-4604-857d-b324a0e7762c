# Android.mk example for using cert-verify-rs in Android NDK projects

LOCAL_PATH := $(call my-dir)

# Prebuilt static library
include $(CLEAR_VARS)
LOCAL_MODULE := cert_verify_rs
LOCAL_SRC_FILES := ../target/$(TARGET_ARCH_ABI)/release/libcert_verify_rs.a
LOCAL_EXPORT_C_INCLUDES := $(LOCAL_PATH)/../include
include $(PREBUILT_STATIC_LIBRARY)

# Your application
include $(CLEAR_VARS)
LOCAL_MODULE := your_app
LOCAL_SRC_FILES := your_app.c
LOCAL_C_INCLUDES := $(LOCAL_PATH)/../include
LOCAL_STATIC_LIBRARIES := cert_verify_rs
LOCAL_LDLIBS := -llog -lm
include $(BUILD_SHARED_LIBRARY)

# Example for building a test executable
include $(<PERSON><PERSON><PERSON>_VARS)
LOCAL_MODULE := cert_verify_test
LOCAL_SRC_FILES := c_example.c
LOCAL_C_INCLUDES := $(LOCAL_PATH)/../include
LOCAL_STATIC_LIBRARIES := cert_verify_rs
LOCAL_LDLIBS := -llog -lm
include $(BUILD_EXECUTABLE)
