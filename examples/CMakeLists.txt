# CMakeLists.txt example for using cert-verify-rs

cmake_minimum_required(VERSION 3.10)
project(cert_verify_example)

# Set C standard
set(CMAKE_C_STANDARD 99)

# Include directories
include_directories(../include)

# Find the static library
if(ANDROID)
    # For Android builds
    set(CERT_VERIFY_LIB_PATH "../target/${ANDROID_ABI}/release/libcert_verify_rs.a")
else()
    # For host builds
    set(CERT_VERIFY_LIB_PATH "../target/release/libcert_verify_rs.a")
endif()

# Import the static library
add_library(cert_verify_rs STATIC IMPORTED)
set_target_properties(cert_verify_rs PROPERTIES
    IMPORTED_LOCATION ${CERT_VERIFY_LIB_PATH}
)

# Create the example executable
add_executable(c_example c_example.c)

# Link libraries
target_link_libraries(c_example cert_verify_rs)

# Platform-specific linking
if(UNIX AND NOT APPLE)
    target_link_libraries(c_example pthread dl m)
elseif(APPLE)
    target_link_libraries(c_example pthread dl m)
elseif(WIN32)
    target_link_libraries(c_example ws2_32 userenv)
endif()

# For Android
if(ANDROID)
    target_link_libraries(c_example log)
endif()
