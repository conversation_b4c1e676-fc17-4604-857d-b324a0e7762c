#ifndef CERT_VERIFY_RS_H
#define CERT_VERIFY_RS_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * ECDSA verification result codes
 */
typedef enum {
    VERIFY_SUCCESS = 0,
    VERIFY_INVALID_SIGNATURE = 1,
    VERIFY_INVALID_PUBLIC_KEY = 2,
    VERIFY_INVALID_INPUT = 3,
    VERIFY_INTERNAL_ERROR = 4
} VerifyResult;

/**
 * Verify ECDSA P-256 signature
 * 
 * @param public_key_ptr Pointer to the public key bytes (64 bytes, uncompressed format without 0x04 prefix)
 * @param public_key_len Length of public key (should be 64)
 * @param message_ptr Pointer to the message bytes
 * @param message_len Length of the message
 * @param signature_ptr Pointer to the signature bytes (64 bytes, r||s format)
 * @param signature_len Length of signature (should be 64)
 * @return VerifyResult enum value
 */
VerifyResult ecdsa_p256_verify(
    const uint8_t* public_key_ptr,
    size_t public_key_len,
    const uint8_t* message_ptr,
    size_t message_len,
    const uint8_t* signature_ptr,
    size_t signature_len
);

/**
 * Get library version string
 * @return Null-terminated version string
 */
const char* get_version(void);

/**
 * Initialize the library
 * @return 0 on success, non-zero on error
 */
int init_library(void);

/**
 * Cleanup the library
 */
void cleanup_library(void);

#ifdef __cplusplus
}
#endif

#endif /* CERT_VERIFY_RS_H */
